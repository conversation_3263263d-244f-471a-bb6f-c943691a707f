// Name: servicetemplateservice
// Type: Angular Service
// Purpose: To provide all server integration for managing reference data (via System Admin)
// Design: On initialisation this service loads the reference data from the server
(function () {
    'use strict';
    var serviceId = 'servicetemplateservice';
    angular.module('appservices').factory(serviceId,
        ['common', 'config', '$http', servicetemplateservice]);

    function servicetemplateservice(common, config, $http) {

        var $q = common.$q;
        var log = common.logger;
        var currentFilter = "";
        var canceller = null;
        var useListCache = false;
        var baseUrl = config.servicesUrlPrefix + 'servicetemplate/';

        const cache = {
            serviceTypesForCategory: {},
            heatingSystemTypesForCategory: {},
        };


        function handleSuccess(response, popupMessage = null) {
            if (response != null && response.data !== undefined) {

                if (popupMessage != null)
                    log.logSuccess(popupMessage);

                return response.data;
            }
            else {
                return null;
            }
        }

        function handleFail(error, message) {
            var msg = `${message}: ${error}`;
            log.logError(msg, error, null, true);
            throw error; // so caller can see it
        }


        function getList(forFilter, fromDate, toDate, pageSize, pageIndex, sort, filter, aggregate) {

            canceller = $q.defer();
            var wkUrl = baseUrl + 'Get';
            if (forFilter == null) {
                forFilter = currentFilter;
            }
            currentFilter = forFilter;
            var params = {fromDate: fromDate, toDate: toDate };
            params = common.buildqueryparameters.build(params, pageSize, pageIndex, sort, filter, aggregate);
            switch (forFilter) {
                case 'Active':
                    params.isDeleted = false;
                    break;
                case 'Deleted':
                    params.isDeleted = true;
                    break;
                default:
                    currentFilter = 'All';
                    break;
            }
            //Get error List from the Server
            return $http({
                url: wkUrl,
                params: params,
                method: 'GET',
                isArray: true,
                cache: useListCache,
                timeout: canceller.promise,
            }).then(handleSuccess, (error) => {

                if (error.status == 0 || error.status == -1) {
                    return;
                } else
                    handleFail(error, "Error getting Servics Template list.");
            });
        }

        function getListCancel() {
            if (canceller != null) {
                canceller.resolve();
            }
        }

        function getListMultiFiltered(pageSize, pageIndex, sort, fields, filterOptions, appliedFilters, searchFilter) {
            canceller = $q.defer();
            var wkUrl = baseUrl + 'GetMultiFiltered';

            let filterData = { fields, filterOptions, appliedFilters };
            let paging = common.buildqueryparameters.build(null, pageSize, pageIndex, sort?.field != null ? [sort] : null, searchFilter);
            filterData.paging = paging;

            return $http.post(wkUrl, filterData).then(handleSuccess, (error) => {
                if (error.status == 0 || error.status == -1) {
                    return;
                } else
                    handleFail(error, "Error getting Service Template multi-filtered list.");
            });
        }

        function getMultiFilterOptions(fieldsList) {
            var url = baseUrl + 'GetMultiFilterOptions';
            var requestData = { fieldsList: fieldsList };
            return $http.post(url, requestData).then(handleSuccess, (error) => handleFail(error, "Error getting Service Template multi-filter options."));
        }

        function getFilterCountData(fields, filterOptions, appliedFilters, searchFilter) {
            var url = baseUrl + 'GetFilterCountData';
            let filterData = { fields, filterOptions, appliedFilters, searchFilter };
            return $http.post(url, filterData).then(handleSuccess, (error) => handleFail(error, "Error getting Service Template filter count data."));
        }

        function getAll() {
            return $http({
                url: baseUrl + 'GetAll',
                method: 'GET',
                cache: true,
            }).then(handleSuccess, (error) => handleFail(error, "Error getting Services Template"));
        }

        function getServiceTemplate(serviceTemplateId) {
            return $http({
                url: baseUrl + 'Get',
                params: { serviceTemplateId},
                method: 'GET',
            }).then(handleSuccess, (error) => handleFail(error, "Error getting Services Template"));
        }

        /* Adds a Surface (Template) to our Surfaces table. */
        function createServiceTemplate(data) {
            var url = baseUrl + 'Create';
            return $http.post(url, data).then(handleSuccess, (e) => handleFail(e, "Error creating Services Template"));
        }

        function updateServiceTemplate(service) {
            var url = baseUrl + 'Update';
            return $http.post(url, service).then(
                (r) => handleSuccess(r, "Services Template Updated"),
                (e) => handleFail(e, "Error updating Surface"));
        }

        function copyServiceTemplate(serviceTemplateId, showSuccessMessage = true) {
            return $http({
                url: baseUrl + 'Copy',
                params: { serviceTemplateId },
                method: 'POST',
            }).then(
                (r) => handleSuccess(r, showSuccessMessage ? "Services Template copied successfully" : null),
                (e) => handleFail(e, "Error copying Services Template"));
        }

        function deleteServiceTemplate(serviceTemplateId, showSuccessMessage = true) {
            return $http({
                url: baseUrl + 'Delete',
                params: { serviceTemplateId },
                method: 'POST',
            }).then(
                (r) => handleSuccess(r, showSuccessMessage ? "Services Template deleted successfully" : null),
                (e) => handleFail(e, "Error deleting Services Template"));
        }

        function undoDeleteServiceTemplate(serviceTemplateId) {
            return $http({
                url: baseUrl + 'UndoDelete',
                params: { serviceTemplateId },
                method: 'POST',
            }).then(
                (r) => handleSuccess(r, "Services Template restored successfully."),
                (e) => handleFail(e, "Error restoring Services Template"));
        }

        function getServiceTypes() {
            return $http({
                url: baseUrl + 'GetServiceTypes',
                method: 'GET',
                cache: true,
            }).then(r => r.data,
                (error) => handleFail(error, "Error getting Service Types"));
        }

        async function getServiceCategories() {
            return $http({
                url: baseUrl + 'GetServiceCategories',
                method: 'GET',
                cache: true,
            }).then(
                r => r.data,
                error => handleFail(error, "Error getting Service Categories")
            );
        }

        function getHeatingSystemTypes() {
            return $http({
                url: baseUrl + 'GetHeatingSystemTypes',
                method: 'GET',
                cache: true,
            }).then(
                r => r.data,
                (error) => handleFail(error, "Error getting Heating System Types")
            );
        }

        function getICRatings() {
            return $http({
                url: baseUrl + 'GetICRatings',
                method: 'GET',
                cache: true,
            }).then(
                r => r.data,
                (error) => handleFail(error, "Error getting IC Ratings"));
        }

        function getServiceControlDevices() {
            return $http({
                url: baseUrl + 'GetServiceControlDevices',
                method: 'GET',
                cache: true,
            }).then(
                r => r.data,
                (error) => handleFail(error, "Error getting Service Control Devices"));
        }

        function getServiceFuelTypes() {
            return $http({
                url: baseUrl + 'GetServiceFuelTypes',
                method: 'GET',
                cache: true,
            }).then(
                r => r.data,
                error => handleFail(error, "Error getting Service Fuel Types")
            );
        }

        function getServicePumpTypes() {
            return $http({
                url: baseUrl + 'GetServicePumpTypes',
                method: 'GET',
                cache: true,
            }).then(
                r => r.data,
                error => handleFail(error, "Error getting Service Pump Types")
            );
        }

        function getServiceBatteryTypes() {
            return $http({
                url: baseUrl + 'GetServiceBatteryTypes',
                method: 'GET',
                cache: true,
            }).then(
                r => r.data,
                error => handleFail(error, "Error getting Service Battery Types")
            );
        }

        /**
         * Uploads an excel file to our server which is then processed into the construction
         * database
         *
         * @param {any} stream The excel file. Should be either the construction OR opening master database excel file.
         * @param {boolean} forceImport If false (the default) the file will not be processed into the DB if any warnings
         *                              or errors are encountered. Pass a value of true if you wish import anyway.
         */
        function uploadTemplateDatabase(stream, forceImport = false) {

            let url = baseUrl + 'UploadTemplateDatabase';
            return $http({
                url: url,
                method: "POST",
                data: stream,
                params: { forceImport },
                headers: {
                    "Content-Type": "application/vnd.ms-excel"
                }
            }).then(
                (data) => handleSuccess(data),
                (error) => handleFail(error, "Error processing excel file!")
            );
        }

        /**
         * Exports the service template database to an Excel file in the same format as expected for imports
         *
         * @param {Array} ids Optional list of service template IDs to export. If not provided, all non-deleted templates will be exported.
         */
        function exportTemplateDatabase(ids) {
            let url = baseUrl + 'ExportTemplateDatabase';

            return $http({
                url: url,
                method: "POST",
                data: ids,
                responseType: "blob"
            }).then(
                (response) => {
                    // Create a dummy anchor element with a data uri and simulate a click on it.
                    // This will show the download pop-up to the user.
                    var a = window.document.createElement('a');
                    a.href = window.URL.createObjectURL(response.data);
                    a.download = "ServicesDatabase.xlsx";
                    document.body.appendChild(a);
                    a.click();

                    // Finally, remove our anchor from the dom.
                    document.body.removeChild(a);

                    log.logSuccess("Services Database exported successfully. See downloads.");
                    return response;
                },
                (error) => handleFail(error, "Error exporting Services database!")
            );
        }

        /**
         * Returns all service types which are available for the given code.
         *
         * @param {string} code The category code to check against.
         * @param {[]} serviceTypes The full array (or a subset if you wish) of service types to check against.
         * */
        function serviceTypesForCategoryCode(code, serviceTypes) {

            let list = [];

            if(serviceTypes == null || serviceTypes.length === 0)
                return [];

            const cached = cache.serviceTypesForCategory[code];
            if(cached != null && cached.length > 0) {
                return cached;
            }

            if (code === "SpaceHeatingSystem")
                list = angular.copy(serviceTypes.filter(x => x.forServiceCategory === "SpaceSystem" || x.forServiceCategory === "SpaceHeatingSystem" || x.forServiceCategory === "All"));

            else if (code === "SpaceCoolingSystem")
                list = angular.copy(serviceTypes.filter(x => x.forServiceCategory === "SpaceSystem" || x.forServiceCategory === "SpaceCoolingSystem" || x.forServiceCategory === "All"));

            else if (code === "HotWaterSystem")
                list = angular.copy(serviceTypes.filter(x => x.forServiceCategory === "HotWaterSystem" || x.forServiceCategory === "All"));

            else if (code === "ArtificialLighting" || code === "ExhaustFans" || code === "CeilingFans")
                list = angular.copy(serviceTypes.filter(x => x.forServiceCategory === "ArtificialLighting" || x.forServiceCategory === "All"));

            else if (code === "Cooktop")
                list = angular.copy(serviceTypes.filter(x => x.forServiceCategory === "Cooktop"));

            else if (code === "Oven")
                list = angular.copy(serviceTypes.filter(x => x.forServiceCategory === "Oven"));

            list.forEach(t => {
                let mapped = SERVICE_TYPE_TITLE_MAPPING.find(m => m.code == t.serviceTypeCode && m.category == code);
                if (mapped != null) {
                    t.title = mapped.title;
                    t.wohLookupTitle = mapped.wohLookupTitle;
                    t.exportTitle = mapped.exportTitle;
                } else {
                    t.wohLookupTitle = t.title;
                    t.exportTitle = t.title;
                }
            });

            cache.serviceTypesForCategory[code] = list.sort((a,b) => a.title > b.title ? 1 : -1);

            return list;
        }

        /**
         * Returns all heating system types which are available for the given code.
         *
         * @param {string} code The category code to check against.
         * @param {[]} heatingSystemTypes The full array (or a subset if you wish) of heating system types to check against.
         * */
        function heatingSystemTypesForCategoryCode(code, heatingSystemTypes) {
            const cached = cache.heatingSystemTypesForCategory[code];
            if(cached != null && cached.length > 0) {
                return cached;
            }

            cache.heatingSystemTypesForCategory[code] = heatingSystemTypes.filter(x => x.forServiceCategory === code || x.forServiceCategory === "All");
            return cache.heatingSystemTypesForCategory[code];
        }

        const SERVICE_TYPE_TITLE_MAPPING = [
            { code: 'ElectricResistanceDucted', category: 'SpaceHeatingSystem', title: 'Electric Resistance (Ducted)',         wohLookupTitle: 'Electric - Resistance (panel)', exportTitle: 'Panel electric resistance' },
            { code: 'ElectricResistancePanel',  category: 'SpaceHeatingSystem', title: 'Electric Resistance Panel',            wohLookupTitle: 'Electric - Resistance (panel)', exportTitle: 'Panel electric resistance' },
            { code: 'ElectricResistanceSlab',   category: 'SpaceHeatingSystem', title: 'Electric Resistance Underfloor',       wohLookupTitle: 'Electric - Resistance (slab)',  exportTitle: 'Slab electric resistance' },
            { code: 'GasDucted',                category: 'SpaceHeatingSystem', title: 'Gas (Ducted)',                         wohLookupTitle: 'Gas (ducted)',                  exportTitle: 'Ducted gas' },
            { code: 'GasNonDucted',             category: 'SpaceHeatingSystem', title: 'Gas (Room)',                           wohLookupTitle: 'Gas (non-ducted)',              exportTitle: 'Non-ducted gas' },
            { code: 'HydronicGas',              category: 'SpaceHeatingSystem', title: 'Hydronic Panel',                       wohLookupTitle: 'Hydronic Gas',                  exportTitle: 'Ducted gas' },
            { code: 'HydronicUnderfloor',       category: 'SpaceHeatingSystem', title: 'Hydronic Underfloor',                  wohLookupTitle: 'Hydronic Gas',                  exportTitle: 'Ducted gas' },
            { code: 'HeatPumpDucted',           category: 'SpaceHeatingSystem', title: 'Reverse-Cycle (Ducted)',               wohLookupTitle: 'Heat Pump (ducted)',            exportTitle: 'Ducted heat pump' },
            { code: 'HeatPumpNonDucted',        category: 'SpaceHeatingSystem', title: 'Reverse-Cycle (Room)',                 wohLookupTitle: 'Heat Pump (non-ducted)',        exportTitle: 'Non-ducted heat pump' },
            { code: 'WoodHeater',               category: 'SpaceHeatingSystem', title: 'Wood',                                 wohLookupTitle: 'Wood Heater',                   exportTitle: 'Wood heater' },
            { code: 'OtherOrNoneSpecified',     category: 'SpaceHeatingSystem', title: 'None/Unknown (Default)',               wohLookupTitle: 'Other or None Specified',       exportTitle: 'Other' },

            { code: 'Evaporative',              category: 'SpaceCoolingSystem', title: 'Evaporative (Ducted)',                 wohLookupTitle: 'Evaporative',                   exportTitle: 'Evaporative' },
            { code: 'EvaporativeRoom',          category: 'SpaceCoolingSystem', title: 'Evaporative (Room)',                   wohLookupTitle: 'Evaporative',                   exportTitle: 'Evaporative' },
            { code: 'HeatPumpDucted',           category: 'SpaceCoolingSystem', title: 'Refrigerative (Ducted)',               wohLookupTitle: 'Heat Pump (ducted)',            exportTitle: 'Ducted heat pump' },
            { code: 'HeatPumpNonDucted',        category: 'SpaceCoolingSystem', title: 'Refrigerative (Room)',                 wohLookupTitle: 'Heat Pump (non-ducted)',        exportTitle: 'Non-ducted heat pump' },
            { code: 'OtherOrNoneSpecified',     category: 'SpaceCoolingSystem', title: 'None/Unknown (Default)',               wohLookupTitle: 'Other or None Specified',       exportTitle: 'Other' },

            { code: 'ElectricInstantaneous',    category: 'HotWaterSystem',     title: 'Electric Instantaneous',               wohLookupTitle: 'Other or None Specified',       exportTitle: 'Gas storage' },
            { code: 'ElectricStorageOffPeak',   category: 'HotWaterSystem',     title: 'Electric Storage (Off-Peak)',          wohLookupTitle: 'Electric Storage (off peak)',   exportTitle: 'Electric storage (off peak)' },
            { code: 'ElectricStorageStandard',  category: 'HotWaterSystem',     title: 'Electric Storage (Peak)',              wohLookupTitle: 'Electric Storage (standard)',   exportTitle: 'Electric storage (standard)' },
            { code: 'GasInstantaneous',         category: 'HotWaterSystem',     title: 'Gas Instantaneous',                    wohLookupTitle: 'Gas Instantaneous',             exportTitle: 'Gas instantaneous' },
            { code: 'GasStorage',               category: 'HotWaterSystem',     title: 'Gas Storage',                          wohLookupTitle: 'Gas Storage',                   exportTitle: 'Gas storage' },
            { code: 'HeatPumpStandard',         category: 'HotWaterSystem',     title: 'Heat Pump (Peak)',                     wohLookupTitle: 'Heat Pump (standard)',          exportTitle: 'Heat pump (standard)' },
            { code: 'HeatPumpOffPeak',          category: 'HotWaterSystem',     title: 'Heat Pump (Off-Peak)',                 wohLookupTitle: 'Heat Pump (off peak)',          exportTitle: 'Heat pump (off peak)' },
            { code: 'SolarElectricStandard',    category: 'HotWaterSystem',     title: 'Solar with Electric Boost (Peak)',     wohLookupTitle: 'Solar Electric (standard)',     exportTitle: 'Solar electric' },
            { code: 'SolarElectricBoost',       category: 'HotWaterSystem',     title: 'Solar with Electric Boost (Off-Peak)', wohLookupTitle: 'Solar Electric (standard)',     exportTitle: 'Solar electric' },
            { code: 'SolarGas',                 category: 'HotWaterSystem',     title: 'Solar with Gas Boost',                 wohLookupTitle: 'Solar Gas',                     exportTitle: 'Solar gas' },
            { code: 'Wood',                     category: 'HotWaterSystem',     title: 'Wood',                                 wohLookupTitle: 'Other or None Specified',       exportTitle: 'Gas storage' },
            { code: 'OtherOrNoneSpecified',     category: 'HotWaterSystem',     title: 'None/Unknown (Default)',               wohLookupTitle: 'Other or None Specified',       exportTitle: 'Gas storage' }
        ];

        const SERVICE_TYPE_CODE_MAPPING = [
            { code: 'ElectricResistanceDucted', wohLookupCode: 'ElectricResistancePanel' },
            { code: 'HydronicUnderfloor',       wohLookupCode: 'HydronicGas' },
            { code: 'EvaporativeRoom',          wohLookupCode: 'Evaporative' },
            { code: 'ElectricInstantaneous',    wohLookupCode: 'OtherOrNoneSpecified' },
            { code: 'Wood',                     wohLookupCode: 'OtherOrNoneSpecified' },
        ];

        // Get mapped title based on code
        function getMappedSystemType(serviceType, forCategory, version) {
            let mapped = SERVICE_TYPE_TITLE_MAPPING.find(m => m.code == serviceType.serviceTypeCode && m.category == forCategory);
            return mapped != null ? mapped[version] : (serviceType.title??serviceType.serviceTypeCode);
        }

        // Get list of Service Types grouped by given categories with each Type having a title of a given version
        function serviceTypesGrouped(
            categories,   // List returned is grouped by these categories
            titleVersion, // The title on each Service Type depends on the title version requested
            list          // This is all the Service Types
        ) {
            let grouped = {};
            categories.forEach(cat => {
                let listForCat = serviceTypesForCategoryCode(cat, list);
                listForCat = common.uniqueValues(listForCat, (item) => item[titleVersion]).sort((a,b) => a.title > b.title ? 1 : -1); // Remove 'wohLookupTitle' duplicates, sort by normal title
                listForCat.push(listForCat.splice(listForCat.indexOf(listForCat.find(t => t.serviceTypeCode == "OtherOrNoneSpecified")), 1)[0]); // Put "None/Unknown (Default)" at end
                listForCat.forEach(t => t.title = t[titleVersion]); // Set main title to the version requested by caller
                grouped[cat] = listForCat;
            });
            return grouped;
        }

        // Get a mapped Service Type code based on a given code which is used for w-o-h lookup for calculations
        function getMappedServiceTypeCode(typeCode) {
            return SERVICE_TYPE_CODE_MAPPING.find(m => m.code == typeCode)?.wohLookupCode ?? typeCode;
        }

        function setIsFavourite(serviceTemplateId, isFavourite) {
            return $http({
                url: baseUrl + 'SetIsFavourite',
                params: { serviceTemplateId, isFavourite },
                method: 'POST',
            }).then(
                (r) => handleSuccess(r),
                (e) => handleFail(e, "Error setting favourite status for Service Template")
            );
        }

        const service = {

            /* These are the operations that are available from this service. */
            getList,
            getListCancel,
            getListMultiFiltered,
            getMultiFilterOptions,
            getFilterCountData,
            getAll,
            currentFilter: function () { return currentFilter },
            getServiceTemplate,
            createServiceTemplate,
            updateServiceTemplate,
            copyServiceTemplate,
            deleteServiceTemplate,
            undoDeleteServiceTemplate,
            getServiceTypes,
            getServiceCategories,
            getHeatingSystemTypes,
            getICRatings,
            getServiceControlDevices,
            getServiceFuelTypes,
            getServicePumpTypes,
            getServiceBatteryTypes,
            uploadTemplateDatabase,
            exportTemplateDatabase,
            serviceTypesForCategoryCode,
            heatingSystemTypesForCategoryCode,
            getMappedSystemType,
            serviceTypesGrouped,
            getMappedServiceTypeCode,
            setIsFavourite
        };

        return service;

    }
})();
